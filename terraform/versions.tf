terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # GitLab-managed Terraform state
  backend "http" {
    # Configuration will be provided via CLI during terraform init
    # See: https://gitlab.uastage.com/help/user/infrastructure/iac/terraform_state
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = var.project_name
      Environment = var.environment
      ManagedBy   = "terraform"
      Repository  = var.repository_url
    }
  }
}
