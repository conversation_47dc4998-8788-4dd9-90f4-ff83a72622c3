# Local Terraform Testing Guide

This guide explains how to test Terraform configurations locally before pushing to GitLab CI/CD.

## Prerequisites

1. **Terraform installed** (>= 1.0)
   ```bash
   # Install via Homebrew (macOS)
   brew install terraform
   
   # Or download from https://www.terraform.io/downloads
   ```

2. **AWS CLI configured** with appropriate credentials
   ```bash
   # Configure AWS CLI
   aws configure
   
   # Or set environment variables
   export AWS_ACCESS_KEY_ID="your-access-key"
   export AWS_SECRET_ACCESS_KEY="your-secret-key"
   export AWS_DEFAULT_REGION="us-east-1"
   ```

## Quick Start

1. **Copy example variables file**:
   ```bash
   # For development environment
   cd terraform/environments/development
   cp terraform.tfvars.example terraform.tfvars
   
   # For production environment
   cd terraform/environments/production
   cp terraform.tfvars.example terraform.tfvars
   ```

2. **Edit terraform.tfvars** with your values:
   ```hcl
   container_image = "registry.gitlab.com/your-group/aws-ecs-demo:dev-latest"
   gitlab_registry_user = "your-gitlab-username"
   gitlab_registry_password = "your-gitlab-token"
   repository_url = "https://gitlab.com/your-group/aws-ecs-demo"
   ```

3. **Run the local plan script**:
   ```bash
   # From the terraform directory
   ./local-plan.sh development
   # or
   ./local-plan.sh production
   ```

## Manual Testing

If you prefer to run Terraform commands manually:

```bash
# Navigate to environment directory
cd terraform/environments/development

# Initialize Terraform (uses local backend)
terraform init

# Validate configuration
terraform validate

# Plan changes
terraform plan

# Apply changes (optional - be careful!)
terraform apply
```

## Required Variables

All environments require these variables in `terraform.tfvars`:

| Variable | Description | Example |
|----------|-------------|---------|
| `container_image` | Docker image URI | `registry.gitlab.com/group/project:tag` |
| `gitlab_registry_user` | GitLab username | `your-username` |
| `gitlab_registry_password` | GitLab token/password | `glpat-xxxxxxxxxxxx` |
| `repository_url` | Git repository URL (optional) | `https://gitlab.com/group/project` |

## Environment Differences

### Development
- CPU: 256 units (0.25 vCPU)
- Memory: 512 MB
- Desired count: 1 instance
- Image tag: typically `dev-latest` or feature branch tags

### Production
- CPU: 512 units (0.5 vCPU)
- Memory: 1024 MB
- Desired count: 2 instances
- Image tag: typically semantic version tags like `v1.0.0`

## Troubleshooting

### Common Issues

1. **AWS credentials not configured**:
   ```
   Error: No valid credential sources found
   ```
   Solution: Configure AWS CLI or set environment variables

2. **terraform.tfvars missing**:
   ```
   Error: terraform.tfvars not found
   ```
   Solution: Copy from terraform.tfvars.example and fill in values

3. **Invalid container image**:
   ```
   Error: Invalid image URI
   ```
   Solution: Ensure image exists in GitLab registry and credentials are correct

### Getting Help

- Check the main [Terraform README](./README.md)
- Review [CI/CD Variables Audit](../CICD_VARIABLES_AUDIT.md)
- Consult [Terraform Implementation Guide](../TERRAFORM_IMPLEMENTATION.md)
