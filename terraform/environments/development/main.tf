# Development Environment Configuration
terraform {
  required_version = ">= 1.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # GitLab-managed Terraform state for development
  backend "http" {
    # Configuration will be provided via CLI during terraform init
    # Example for development:
    # terraform init \
    #   -backend-config="address=https://gitlab.uastage.com/api/v4/projects/PROJECT_ID/terraform/state/development" \
    #   -backend-config="lock_address=https://gitlab.uastage.com/api/v4/projects/PROJECT_ID/terraform/state/development/lock" \
    #   -backend-config="unlock_address=https://gitlab.uastage.com/api/v4/projects/PROJECT_ID/terraform/state/development/lock" \
    #   -backend-config="username=gitlab-ci-token" \
    #   -backend-config="password=$CI_JOB_TOKEN" \
    #   -backend-config="lock_method=POST" \
    #   -backend-config="unlock_method=DELETE" \
    #   -backend-config="retry_wait_min=5"
  }
}

# Use the main Terraform configuration
module "infrastructure" {
  source = "../.."

  # Environment Configuration
  project_name = "aws-ecs-demo"
  environment  = "development"

  # AWS Configuration
  aws_region = "us-east-1"

  # Container Configuration
  container_image  = var.container_image
  container_port   = 3000
  container_cpu    = 256
  container_memory = 512

  # ECS Configuration
  desired_count = 1

  # GitLab Registry Authentication
  gitlab_registry_user     = var.gitlab_registry_user
  gitlab_registry_password = var.gitlab_registry_password
}

# Development-specific variables
variable "container_image" {
  description = "Docker container image URI for development"
  type        = string
}

variable "gitlab_registry_user" {
  description = "GitLab registry username"
  type        = string
}

variable "gitlab_registry_password" {
  description = "GitLab registry password"
  type        = string
  sensitive   = true
}

variable "repository_url" {
  description = "URL of the Git repository (for tagging resources)"
  type        = string
  default     = ""
}

# Development-specific outputs
output "application_url" {
  description = "Development application URL"
  value       = module.infrastructure.application_url
}

output "deployed_image" {
  description = "Container image deployed to development"
  value       = module.infrastructure.deployed_image
}

output "ecs_cluster_name" {
  description = "Development ECS cluster name"
  value       = module.infrastructure.ecs_cluster_name
}

output "ecs_service_name" {
  description = "Development ECS service name"
  value       = module.infrastructure.ecs_service_name
}

output "gitlab_registry_secret_arn" {
  description = "GitLab registry authentication secret ARN"
  value       = module.infrastructure.gitlab_registry_secret_arn
}
